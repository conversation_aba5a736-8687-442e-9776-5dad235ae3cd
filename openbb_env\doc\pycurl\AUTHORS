Copyright (C) 2001-2008 by <PERSON><PERSON><PERSON> <kjet<PERSON><PERSON> at gmail.com>
Copyright (C) 2001-2008 by <PERSON> <markus at oberhumer.com>
Copyright (C) 2013-2022 by <PERSON><PERSON> <code at olegp.name>

Please see README, COPYING-LGPL and COPYING-MIT for license information.

The following individuals contributed code to PycURL:

<PERSON> <visine19 at hotmail.com>
<PERSON> <therigu at users.sourceforge.net>
<PERSON> <adam at isprime.com>
<PERSON><PERSON><PERSON> <akiomik at gmail.com>
<PERSON> <pion at afnic.fr>
<PERSON> <amir.rossert at safebreach.com>
Amit Mongia <amit_mongia at hotmail.com>
<PERSON><PERSON><PERSON><PERSON> <comel at vingd.com>
<PERSON><PERSON><PERSON> <khan.m.arshad at gmail.com>
<PERSON><PERSON> <asobierak at gmail.com>
<PERSON> <ashleyw at activestate.com>
<PERSON> <barry at python.org>
<PERSON><PERSON><PERSON> <benjamin at python.org>
<PERSON> <bill.collins at hp.com>
<PERSON> <mail at boanderson.me>
<PERSON> <camiller at linkedin.com>
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <yan12125 at gmail.com>
<PERSON> <cclauss at me.com>
<PERSON> <PERSON> <cwarner at kernelcode.com>
<PERSON> <PERSON> <clintclayton at me.com>
<PERSON>eenberg <conrad at hep.caltech.edu>
Daniel Pena <PERSON>aga <dpena at ph.tum.de>
<PERSON> Stenberg <daniel at haxx.se>
decitre <decitre at gmail.com>
Dima Tisnek <dimaqq at gmail.com>
Dmitriy Taychenachev <dmitriy.taychenachev at skypicker.com>
Dmitry Ketov <dketov at gmail.com>
Dom Sekotill <dom.sekotill at kodo.org.uk>
Domenico Andreoli <cavok at libero.it>
Dominique <curl-and-python at d242.net>
Eneas U de Queiroz <cotequeiroz at gmail.com>
Eric S. Raymond <esr at thyrsus.com>
Felix Yan <felixonmars at archlinux.org>
Francisco Alves <chico at corp.globo.com>
Gabi Davar <grizzly.nyo at gmail.com>
Gisle Vanem <gvanem at yahoo.no>
Gregory Petukhov <lorien at lorien.name>
Hasan <aliyevH at hotmail.com>
Hugo <hugovk at users.noreply.github.com>
Iain R. Learmonth <irl at fsfe.org>
ideal <idealities at gmail.com>
Jakob Truelsen <jakob at scalgo.com>
Jakub Wilk <jwilk at jwilk.net>
James Deucker <bitwisecook at users.noreply.github.com>
Jan Kryl <jan.kryl at nexenta.com>
Jayne <corvine at gmail.com>
James Deucker <bitwisecook at users.noreply.github.com>
Jean Hominal <jhominal at gmail.com>
JiCiT <jason at infinitebubble.com>
Jim Patterson
Josef Schlehofer <pepe.schlehofer at gmail.com>
Jozef Melicher <jozef.melicher at eset.sk>
K.S.Sreeram <sreeram at tachyontech.net>
Kamil Dudka <kdudka at redhat.com>
Kevin Ko <kevin.s.ko at gmail.com>
Kevin Schlosser <drschlosser at hotmail.com>
Khavish Anshudass Bhundoo <khavishbhundoo at users.noreply.github.com>
Kian-Meng Ang <kianmeng at cpan.org>
kxrd <onyeabor at riseup.net>
Lipin Dmitriy <blackwithwhite666 at gmail.com>
Léo El Amri <leo at superlel.me>
Marc Labranche <mlabranche at developertown.com>
Marcel Brouwers <marcel at marcelbrouwers.nl>
Marcelo Jorge Vieira <metal at alucinados.com>
Marien Zwart <marienz at users.sourceforge.net>
Mark Eichin
Markus <nepenthesdev at gmail.com>
Martin Muenstermann <mamuema at sourceforge.net>
Matt King <matt at gnik.com>
Michael C <michael at mchang.name>
Michael Cho <michael at michaelcho.dev>
Michael Coughlin <michael.w.coughlin at gmail.com>
Michael Treanor <26148512+skeptycal at users.noreply.github.com>
Michał Górny <mgorny at gentoo.org>
Miro Hrončok <miro at hroncok.cz>
Nelson Chen <crazysim at gmail.com>
Nick Pilon <npilon at oreilly.com>
Nicolas Pauss <nicolas.pauss at intersec.com>
Oleg Broytman <phd at phdru.name>
Oren <orenyomtov at users.noreply.github.com>
Orion Poplawski <orion at cora.nwra.com>
Oskari Saarenmaa <os at ohmu.fi>
Paul Pacheco
Pavel Horáček <horacek.pavel at protonmail.com>
Pierre Grimaud <grimaud.pierre at gmail.com>
René Dudfield <renesd at gmail.com>
resokou <resokou at gmail.com>
Roland Sommer <rol at ndsommer.de>
Romuald Brunet <romuald at gandi.net>
Romulo A. Ceccon <romuloceccon at gmail.com>
Russell McConnachie <okanaganrusty at mcconnachie.ca>
Russell McConnachie <pmcconna at cisco.com>
Samuel Dion-Girardeau <samuel.diongirardeau at gmail.com>
Samuel Henrique <samueloph at debian.org>
Scott Talbert <swt at techie.net>
Simon Legner <Simon.Legner at gmail.com>
Srinivas <spg349 at nyu.edu>
Steve Kowalik <steven at wedontsleep.org>
Subin <eourm20 at gmail.com>
Tal Einat <tal.einat at socialcodeinc.com>
Thomas Hunger <teh at camvine.org>
Tino Lange <Tino.Lange at gmx.de>
toddrme2178 <toddrme2178 at gmail.com>
Tom Pierce <tom.pierce0 at gmail.com>
Vesa Jääskeläinen <vesa.jaaskelainen at vaisala.com>
Victor Lascurain <bittor at eleka.net>
Vincent Philippon <Vincent.Philippon at ubisoft.com>
Vitaly Murashev <vitaly.murashev at gmail.com>
Vitezslav Cizek <vcizek at suse.com>
vmurashev <vitaly.murashev at gmail.com>
Wei C <gitsouler at users.noreply.github.com>
Whitney Sorenson <wsorenson at gmail.com>
Wim Lewis <wiml at users.sourceforge.net>
Yiteng Zhang <yiteng.zhang at oracle.com>
Yuhui H <eyecat at gmail.com>
Yuri Ushakov <yuri.ushakov at gmail.com>
Yves Bastide <yves at botify.com>
Zdenek Pavlas <zpavlas at redhat.com>
ziggy <ziggy at elephant-bird.net>
