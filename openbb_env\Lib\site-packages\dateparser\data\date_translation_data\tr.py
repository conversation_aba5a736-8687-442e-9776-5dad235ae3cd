info = {
    "name": "tr",
    "date_order": "DMY",
    "january": [
        "oca",
        "ocak",
        "Oc"
    ],
    "february": [
        "şub",
        "şubat",
        "Şu"
    ],
    "march": [
        "mar",
        "mart"
    ],
    "april": [
        "nis",
        "nisan",
        "Ni"
    ],
    "may": [
        "may",
        "mayıs"
    ],
    "june": [
        "haz",
        "haziran",
        "Ha"
    ],
    "july": [
        "tem",
        "temmuz",
        "Te"
    ],
    "august": [
        "ağu",
        "ağustos",
        "Ağ"
    ],
    "september": [
        "eyl",
        "eylül",
        "Ey"
    ],
    "october": [
        "eki",
        "ekim",
        "Ek"
    ],
    "november": [
        "kas",
        "kasım",
        "Ka"
    ],
    "december": [
        "ara",
        "aralık",
        "Ar"
    ],
    "monday": [
        "pazar<PERSON>i",
        "pzt"
    ],
    "tuesday": [
        "sal",
        "salı"
    ],
    "wednesday": [
        "çar",
        "çarşamba",
        "Çrs"
    ],
    "thursday": [
        "per",
        "perşembe",
        "Prs"
    ],
    "friday": [
        "cum",
        "cuma"
    ],
    "saturday": [
        "cmt",
        "cumartesi"
    ],
    "sunday": [
        "paz",
        "pazar"
    ],
    "am": [
        "öö"
    ],
    "pm": [
        "ös"
    ],
    "year": [
        "yıl",
        "sene"
    ],
    "month": [
        "ay"
    ],
    "week": [
        "hafta",
        "hf"
    ],
    "day": [
        "gün"
    ],
    "hour": [
        "sa",
        "saat"
    ],
    "minute": [
        "dakika",
        "dk"
    ],
    "second": [
        "saniye",
        "sn"
    ],
    "relative-type": {
        "0 day ago": [
            "bugün"
        ],
        "0 hour ago": [
            "bu saat"
        ],
        "0 minute ago": [
            "bu dakika"
        ],
        "0 month ago": [
            "bu ay"
        ],
        "0 second ago": [
            "şimdi"
        ],
        "0 week ago": [
            "bu hafta"
        ],
        "0 year ago": [
            "bu yıl"
        ],
        "1 day ago": [
            "dün",
            "geçen gün"
        ],
        "1 month ago": [
            "geçen ay"
        ],
        "1 week ago": [
            "geçen hafta"
        ],
        "1 year ago": [
            "geçen yıl"
        ],
        "in 1 day": [
            "yarın",
            "önümüzdeki gün"
        ],
        "in 1 month": [
            "gelecek ay",
            "önümüzdeki ay"
        ],
        "in 1 week": [
            "gelecek hafta",
            "haftaya",
            "önümüzdeki hafta"
        ],
        "in 1 year": [
            "gelecek yıl",
            "önümüzdeki yıl"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "(\\d+[.,]?\\d*) gün önce"
        ],
        "\\1 hour ago": [
            "(\\d+[.,]?\\d*) sa önce",
            "(\\d+[.,]?\\d*) saat önce"
        ],
        "\\1 minute ago": [
            "(\\d+[.,]?\\d*) dakika önce",
            "(\\d+[.,]?\\d*) dk önce"
        ],
        "\\1 month ago": [
            "(\\d+[.,]?\\d*) ay önce"
        ],
        "\\1 second ago": [
            "(\\d+[.,]?\\d*) saniye önce",
            "(\\d+[.,]?\\d*) sn önce"
        ],
        "\\1 week ago": [
            "(\\d+[.,]?\\d*) hafta önce",
            "(\\d+[.,]?\\d*) hf önce"
        ],
        "\\1 year ago": [
            "(\\d+[.,]?\\d*) yıl önce"
        ],
        "in \\1 day": [
            "(\\d+[.,]?\\d*) gün sonra"
        ],
        "in \\1 hour": [
            "(\\d+[.,]?\\d*) sa sonra",
            "(\\d+[.,]?\\d*) saat sonra"
        ],
        "in \\1 minute": [
            "(\\d+[.,]?\\d*) dakika sonra",
            "(\\d+[.,]?\\d*) dk sonra"
        ],
        "in \\1 month": [
            "(\\d+[.,]?\\d*) ay sonra"
        ],
        "in \\1 second": [
            "(\\d+[.,]?\\d*) saniye sonra",
            "(\\d+[.,]?\\d*) sn sonra"
        ],
        "in \\1 week": [
            "(\\d+[.,]?\\d*) hafta sonra",
            "(\\d+[.,]?\\d*) hf sonra"
        ],
        "in \\1 year": [
            "(\\d+[.,]?\\d*) yıl sonra"
        ]
    },
    "locale_specific": {
        "tr-CY": {
            "name": "tr-CY"
        }
    },
    "skip": [
        "ve",
        "yaklaşık",
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ],
    "sentence_splitter_group": 1,
    "ago": [
        "önce"
    ],
    "in": [
        "içerisinde",
        "içinde",
        "sonra"
    ]
}
